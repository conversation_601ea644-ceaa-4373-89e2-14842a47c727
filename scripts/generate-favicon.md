# Favicon Generation Guide

## Quick Setup (Recommended)

1. **Go to [favicon.io](https://favicon.io/favicon-converter/)**
2. **Upload your `/public/logo.png` file**
3. **Click "Download" to get the favicon package**
4. **Extract and copy these files to your `/public` directory:**
   - `favicon.ico`
   - `favicon-16x16.png`
   - `favicon-32x32.png`
   - `apple-touch-icon.png`
   - `android-chrome-192x192.png`
   - `android-chrome-512x512.png`

## Alternative: Manual Generation

If you prefer to generate favicons manually:

### Using ImageMagick (if installed):
```bash
# Install ImageMagick first
# macOS: brew install imagemagick
# Ubuntu: sudo apt-get install imagemagick

# Generate different sizes
convert public/logo.png -resize 16x16 public/favicon-16x16.png
convert public/logo.png -resize 32x32 public/favicon-32x32.png
convert public/logo.png -resize 180x180 public/apple-touch-icon.png
convert public/logo.png -resize 192x192 public/android-chrome-192x192.png
convert public/logo.png -resize 512x512 public/android-chrome-512x512.png

# Generate ICO file
convert public/logo.png -resize 32x32 public/favicon.ico
```

### Using Online Tools:
- **favicon.io** - Best overall
- **realfavicongenerator.net** - Most comprehensive
- **favicon-generator.org** - Simple and fast

## Files You Need:

After generation, your `/public` directory should contain:
```
public/
├── logo.png (your original logo)
├── favicon.ico
├── favicon-16x16.png
├── favicon-32x32.png
├── apple-touch-icon.png
├── android-chrome-192x192.png
├── android-chrome-512x512.png
└── site.webmanifest (already created)
```

## Verification:

1. **Test in browser**: Check if favicon appears in browser tab
2. **Test on mobile**: Add to home screen to test app icons
3. **Use favicon checker**: [realfavicongenerator.net/favicon_checker](https://realfavicongenerator.net/favicon_checker)

## Notes:

- The favicon system is already implemented in your `app/layout.tsx`
- Theme color is set to your brand green: `#4ade80`
- Background color is set to black: `#000000`
- All metadata is properly configured for SEO and social sharing
