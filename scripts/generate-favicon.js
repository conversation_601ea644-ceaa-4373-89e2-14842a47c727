#!/usr/bin/env node

/**
 * Favicon Generator Script
 * 
 * This script generates favicon files from your logo.png
 * 
 * Prerequisites:
 * npm install sharp (for image processing)
 * 
 * Usage:
 * node scripts/generate-favicon.js
 */

const fs = require('fs');
const path = require('path');

// Check if sharp is available
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.log('❌ Sharp not found. Please install it first:');
  console.log('npm install sharp');
  console.log('\nOr use the online method described in generate-favicon.md');
  process.exit(1);
}

const logoPath = path.join(__dirname, '../public/logo.png');
const publicDir = path.join(__dirname, '../public');

// Check if logo exists
if (!fs.existsSync(logoPath)) {
  console.log('❌ Logo file not found at public/logo.png');
  process.exit(1);
}

const sizes = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 },
];

async function generateFavicons() {
  console.log('🚀 Generating favicons from logo.png...\n');

  try {
    // Generate PNG favicons
    for (const { name, size } of sizes) {
      const outputPath = path.join(publicDir, name);
      await sharp(logoPath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        })
        .png()
        .toFile(outputPath);
      
      console.log(`✅ Generated ${name} (${size}x${size})`);
    }

    // Generate ICO file (using 32x32 PNG)
    const icoPath = path.join(publicDir, 'favicon.ico');
    await sharp(logoPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png()
      .toFile(icoPath);
    
    console.log('✅ Generated favicon.ico (32x32)');

    console.log('\n🎉 All favicons generated successfully!');
    console.log('\nGenerated files:');
    sizes.forEach(({ name }) => console.log(`  - ${name}`));
    console.log('  - favicon.ico');
    
    console.log('\n📝 Next steps:');
    console.log('1. Check your browser tab for the new favicon');
    console.log('2. Test on mobile devices');
    console.log('3. Verify with: https://realfavicongenerator.net/favicon_checker');

  } catch (error) {
    console.error('❌ Error generating favicons:', error.message);
    process.exit(1);
  }
}

generateFavicons();
