"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Spark<PERSON>, ChevronDown } from "lucide-react"
import { ScrollToButton } from "@/components/common/scroll-to-button"
import { Logo } from "@/components/ui/logo"
import { stickyHeader } from "@/lib/animations"
import { SECTION_IDS } from "@/lib/constants"

export function StickyHeader() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show sticky header after scrolling 300px
      const scrollPosition = window.scrollY
      setIsVisible(scrollPosition > 300)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          {...stickyHeader}
          className="fixed top-0 left-0 right-0 z-50"
        >
          {/* Solid black backdrop with thin border */}
          <div className="absolute inset-0 bg-black border-b border-primary/30 z-0" />
          
          {/* Bottom border - solid line */}
          <div className="absolute bottom-0 left-0 right-0 h-px bg-primary z-10" />
          
          <div className="container mx-auto px-6 relative z-10">
            <div className="flex justify-between items-center py-3">
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                whileHover={{ x: 3, transition: { duration: 0.2 } }}
              >
                <div className="relative cursor-pointer">
                  <Logo
                    size="sm"
                    variant="default"
                    textClassName="text-lg"
                  />

                  {/* Status indicator */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary"></div>
                </div>
              </motion.div>

              <div className="flex items-center gap-6">
                {/* Navigation links */}
                <motion.div
                  className="hidden lg:flex gap-6 xl:gap-8 overflow-x-auto no-scrollbar"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  {[
                    { href: "#features", label: "Features" },
                    { href: "#testimonials", label: "Reviews" },
                    { href: "#pricing", label: "Pricing" },
                    { href: "#faq", label: "FAQ" },
                    { href: "#contact", label: "Contact us" }
                  ].map((link, index) => (
                    <motion.a 
                      key={link.href}
                      href={link.href}
                      className="font-mono font-medium text-xs xl:text-sm text-white/70 hover:text-primary cursor-pointer transition-colors relative group uppercase whitespace-nowrap"
                      whileHover={{ y: -2 }}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 + (index * 0.05) }}
                    >
                      {link.label}
                      <div className="absolute -bottom-1 left-0 w-0 h-px bg-primary group-hover:w-full transition-all duration-300" />
                    </motion.a>
                  ))}
                </motion.div>
                
                {/* CTA Button */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ScrollToButton
                    targetId={SECTION_IDS.pricing}
                    variant="secondary"
                    size="sm"
                    className="group relative overflow-hidden"
                  >
                    <span className="relative z-10 flex items-center gap-1">
                      Get Started
                      <ChevronDown className="w-4 h-4 mt-0.5 group-hover:translate-y-0.5 transition-transform duration-300" />
                    </span>
                  </ScrollToButton>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
