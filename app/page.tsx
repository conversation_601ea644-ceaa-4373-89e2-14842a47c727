// Core sections
import { HeroSection } from "@/components/hero-section"
import { BeforeAfterSection } from "@/components/before-after-section"
import { ProductStack } from "@/components/product-stack"
import { ProductHighlights } from "@/components/product-highlights"
import { CredibilitySection } from "@/components/credibility-section"
import { FaqSection } from "@/components/faq-section"
import { MoneyBackGuarantee } from "@/components/money-back-guarantee"
import { SocialContact } from "@/components/social-contact"

// Layout components
import { StickyHeader } from "@/components/sticky-header"
import { Footer } from "@/components/footer"
import { FloatingCTA } from "@/components/floating-cta"
import { SectionContainer } from "@/components/section-container"


export default function HomePage() {
  return (
    <main className="relative">
      <StickyHeader />

      {/* Hero Section */}
      <SectionContainer id="hero" fullWidth={true}>
        <HeroSection />
      </SectionContainer>

      {/* Before/After Section */}
      <SectionContainer id="before-after" withGreenLine={true}>
        <BeforeAfterSection />
      </SectionContainer>

      {/* Product Stack */}
      <SectionContainer id="pricing" fullWidth={true} withGreenLine={true}>
        <ProductStack />
      </SectionContainer>

      {/* Product Highlights */}
      <SectionContainer id="features">
        <ProductHighlights />
      </SectionContainer>

      {/* Credibility Section */}
      <SectionContainer id="testimonials" fullWidth={true}>
        <CredibilitySection />
      </SectionContainer>

      {/* FAQ Section */}
      <SectionContainer id="faq" fullWidth={true} withGreenLine={true}>
        <FaqSection />
      </SectionContainer>

      {/* Money Back Guarantee */}
      <SectionContainer id="guarantee">
        <MoneyBackGuarantee />
      </SectionContainer>

      {/* Social Contact */}
      <SectionContainer id="contact" withGreenLine={true}>
        <SocialContact />
      </SectionContainer>

      <Footer />
      <FloatingCTA />
    </main>
  )
}
