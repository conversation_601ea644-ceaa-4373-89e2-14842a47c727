import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/contexts/auth-context"


// Typography fonts according to design system
const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
  display: "swap",
  weight: ["400"], // For statistics, numbers, and buttons only
})

export const metadata: Metadata = {
  title: "TRADEFORM",
  description: "",
  keywords: "trading, AI trading, investment, financial markets",
  authors: [{ name: "Tradeform Team" }],
  openGraph: {
    title: "TRADEFORM",
    description: "",
    images: ['/logo.png'],
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${jetbrainsMono.variable}`}>
      <head>
        <link href="https://api.fontshare.com/v2/css?f[]=satoshi@400,700,800&display=swap" rel="stylesheet" />
      </head>
      <body className="font-satoshi antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
